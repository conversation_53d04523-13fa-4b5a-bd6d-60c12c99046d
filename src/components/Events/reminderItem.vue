<template>
  <div class="reminder-item" @click="handleClick">
    <!-- 右上角删除按钮 -->
    <button class="delete-btn" @click.stop="handleDeleteClick">
      <DeleteIcon :size="30" color="var(--primary-color)" />
    </button>
    <div class="reminder-text">
      {{ truncateText(reminderData.reminder_text_template || '暂无提醒内容') }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { type IReminder } from '@/apis/memory';
import DeleteIcon from '@/assets/icons/DeleteIcon.vue';

interface IProps {
  reminderData: IReminder;
}

const props = defineProps<IProps>();

const emit = defineEmits<{
  click: [reminder: IReminder];
  deleteRequest: [reminder: IReminder];
}>();

const handleClick = () => {
  emit('click', props.reminderData);
};

// 处理删除按钮点击
const handleDeleteClick = () => {
  emit('deleteRequest', props.reminderData);
};

// 截断文本，只显示前四个字加省略号
const truncateText = (text: string): string => {
  if (!text) return '';
  if (text.length <= 4) return text;
  return text.substring(0, 4) + '...';
};
</script>

<style scoped lang="scss">
.reminder-item {
  width: 220px;
  height: 56px; // 固定高度与add-reminder-card保持一致
  min-height: 56px;
  max-height: 56px;
  // 使用bgGlass背景
  background: var(--bg-glass);
  border: none;
  border-radius: 12px;
  padding: 10px 12px;
  box-sizing: border-box;
  backdrop-filter: blur(10px);
  border-left: 5px solid var(--accent-color);
  box-shadow: 0 6px 18px -6px color-mix(in oklab, var(--accent-color) 55%, transparent), var(--shadow-accent), inset 0 0 0 1px color-mix(in oklab, var(--accent-color) 45%, transparent);
  transition: all 0.3s ease;
  cursor: pointer;
  color: var(--page-text-primary);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: stretch;
  position: relative;
  overflow: hidden;
  margin: 0; // 避免与底部对话框之间产生间隙

  &:active {
    transform: translateY(-1px);
  }
}

// 删除按钮样式，与personDetailPopup中的close-btn保持一致
.delete-btn {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 22px;
  height: 22px;
  display: flex;
  background: transparent;
  border: none;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  z-index: 10;
  .delete-icon {
    width: 14px;
    height: 14px;
    filter: brightness(0) invert(1);
  }
}

.reminder-text {
  margin: 0;
  text-align: center;
  color: '#000000';
  font-size: 28px; // 与add-reminder-card保持一致
  font-weight: 500;
  line-height: 1.4;
  word-break: break-word;
  letter-spacing: 0.5px;
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding-right: 30px; // 为删除按钮留出空间
}
</style>
